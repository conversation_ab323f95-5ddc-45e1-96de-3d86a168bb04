#include "Motor.h"

//TB6612控制宏定义
#define TB6612_ENABLE()     DL_GPIO_setPins(TB6612_STBY_PORT, TB6612_STBY_STBY_PIN)
#define TB6612_DISABLE()    DL_GPIO_clearPins(TB6612_STBY_PORT, TB6612_STBY_STBY_PIN)

//左前轮(电机A)
MOTOR_Def_t Motor_Font_Left = {.Motor_Dirc = DIRC_FOWARD,
                               .Motor_Encoder_Addr = &Data_MotorEncoder[0],
                               .Motor_IN1_Pin = DIRC_CTRL_FONT_LEFT_PIN,
                               .Motor_IN2_Pin = DIRC_CTRL_IN2_FONT_LEFT_IN2_PIN,
                               .Motor_PWM_TIMX = MotorFront_INST,
                               .Motor_PWM_CH = DL_TIMER_CC_0_INDEX};
//右前轮(电机B)
MOTOR_Def_t Motor_Font_Right = {.Motor_Dirc = DIRC_FOWARD,
                                .Motor_Encoder_Addr = &Data_MotorEncoder[1],
                                .Motor_IN1_Pin = DIRC_CTRL_FONT_RIGHT_PIN,
                                .Motor_IN2_Pin = DIRC_CTRL_IN2_FONT_RIGHT_IN2_PIN,
                                .Motor_PWM_TIMX = MotorFront_INST,
                                .Motor_PWM_CH = DL_TIMER_CC_1_INDEX};
//左后轮(电机C)
MOTOR_Def_t Motor_Back_Left = {.Motor_Dirc = DIRC_FOWARD,
                               .Motor_Encoder_Addr = &Data_MotorEncoder[2],
                               .Motor_IN1_Pin = DIRC_CTRL_BACK_LEFT_PIN,
                               .Motor_IN2_Pin = DIRC_CTRL_IN2_BACK_LEFT_IN2_PIN,
                               .Motor_PWM_TIMX = MotorBack_INST,
                               .Motor_PWM_CH = DL_TIMER_CC_0_INDEX};
//右后轮(电机D)
MOTOR_Def_t Motor_Back_Right = {.Motor_Dirc = DIRC_FOWARD,
                                .Motor_Encoder_Addr = &Data_MotorEncoder[3],
                                .Motor_IN1_Pin = DIRC_CTRL_BACK_RIGHT_PIN,
                                .Motor_IN2_Pin = DIRC_CTRL_IN2_BACK_RIGHT_IN2_PIN,
                                .Motor_PWM_TIMX = MotorBack_INST,
                                .Motor_PWM_CH = DL_TIMER_CC_1_INDEX};
/**
 * @brief 开启电机
 *
 */
void Motor_Start(void)
{
    DL_TimerG_startCounter(MotorFront_INST); //开启前轮PWM
    DL_TimerG_startCounter(MotorBack_INST); //开启后轮PWM

    TB6612_ENABLE(); //使能TB6612

    /*设置所有占空比都为0*/
    Motor_SetDuty(&Motor_Font_Left, 0.0f);
    Motor_SetDuty(&Motor_Back_Left, 0.0f);
    Motor_SetDuty(&Motor_Back_Right, 0.0f);
    Motor_SetDuty(&Motor_Font_Right, 0.0f);

    /*初始化所有PID对象*/
    PID_IQ_Init(&Motor_Font_Left.Motor_PID_Instance);
    PID_IQ_Init(&Motor_Back_Left.Motor_PID_Instance);
    PID_IQ_Init(&Motor_Back_Right.Motor_PID_Instance);
    PID_IQ_Init(&Motor_Font_Right.Motor_PID_Instance);

    /*PID 系数初值*/
    PID_IQ_SetParams(&Motor_Font_Left.Motor_PID_Instance, 2.0f, 0.5f, 0.1f);
    PID_IQ_SetParams(&Motor_Back_Left.Motor_PID_Instance, 2.0f, 0.5f, 0.1f);
    PID_IQ_SetParams(&Motor_Back_Right.Motor_PID_Instance, 2.0f, 0.5f, 0.1f);
    PID_IQ_SetParams(&Motor_Font_Right.Motor_PID_Instance, 2.0f, 0.5f, 0.1f);
}

/**
 * @brief TB6612使能控制
 *
 * @param enable true:使能 false:禁用
 */
void Motor_Enable(bool enable)
{
    if (enable) {
        TB6612_ENABLE();
    } else {
        TB6612_DISABLE();
    }
}

/**
 * @brief 设置电机正反转(TB6612 IN1/IN2控制)
 *
 * @param Motor 电机实例
 * @param Dirc 方向
 * @return 返回设置成功与否
 */
static bool Motor_SetDirc(MOTOR_Def_t *Motor, Motor_DIRC_Def_t Dirc)
{
    if (Motor == NULL) return false;

    if (Dirc == DIRC_FOWARD) {
        //正转: IN1=0, IN2=1
        DL_GPIO_setPins(DIRC_CTRL_PORT, Motor->Motor_IN1_Pin);
        DL_GPIO_clearPins(DIRC_CTRL_IN2_PORT, Motor->Motor_IN2_Pin);
        Motor->Motor_Dirc = DIRC_FOWARD;
        return true;
    }
    else if (Dirc == DIRC_BACKWARD) {
        //反转: IN1=1, IN2=0
        DL_GPIO_clearPins(DIRC_CTRL_PORT, Motor->Motor_IN1_Pin);
        DL_GPIO_setPins(DIRC_CTRL_IN2_PORT, Motor->Motor_IN2_Pin);
        Motor->Motor_Dirc = DIRC_BACKWARD;
        return true;
    }
    else if (Dirc == DIRC_NONE) {
        //制动: IN1=0, IN2=0
        DL_GPIO_clearPins(DIRC_CTRL_PORT, Motor->Motor_IN1_Pin);
        DL_GPIO_clearPins(DIRC_CTRL_IN2_PORT, Motor->Motor_IN2_Pin);
        Motor->Motor_Dirc = DIRC_NONE;
        return true;
    }
    return false;
}

/**
 * @brief 设置对应的电机占空比值(TB6612控制)
 *
 * @param Motor 电机实例
 * @param value PWM占空比 (-100 ~ +100, 负值反转，正值正转)
 * @return 返回设置成功与否
 */
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value)
{
    if (Motor == NULL) return false;

    // 限制范围
    if (value > 100.0f) value = 100.0f;
    if (value < -100.0f) value = -100.0f;

    // 根据符号设置方向
    if (value > 0) {
        Motor_SetDirc(Motor, DIRC_FOWARD);
    }
    else if (value < 0) {
        Motor_SetDirc(Motor, DIRC_BACKWARD);
    }
    else {
        Motor_SetDirc(Motor, DIRC_NONE); //停止时制动
    }

    // 设置PWM占空比（使用绝对值）
    uint32_t duty = (uint32_t)fabs(value);
    DL_TimerG_setCaptureCompareValue(Motor->Motor_PWM_TIMX, duty, Motor->Motor_PWM_CH);

    return true;
}

/**
 * @brief 获取电机速度 更新到PID实例中
 * 
 * @param Motor 电机
 * @param time 读取时间间隔 ms
 * @return true 
 * @return false 
 */
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time)
{
    if (Motor == NULL) return false;

    _iq Interval_Time = _IQ(time / 1000.0f); // 转换为秒
    _iq Encoder_Value = _IQ(*Motor->Motor_Encoder_Addr); // 获取编码值
    *Motor->Motor_Encoder_Addr = 0; // 编码清空

    // 计算速度 (转/秒 或 脉冲/秒)
    _iq Speed = _IQdiv(Encoder_Value, Interval_Time);

    // 根据电机方向调整速度符号
    if (Motor->Motor_Dirc == DIRC_BACKWARD && Speed > 0)
    {
        Speed = -Speed; // 反转时速度为负
    }
    else if (Motor->Motor_Dirc == DIRC_FOWARD && Speed < 0)
    {
        Speed = -Speed; // 正转时速度为正
    }

    Motor->Motor_PID_Instance.Acutal_Now = Speed;

    return true;
}

/**
 * @brief TB6612电机驱动测试函数
 *
 */
void Motor_Test_TB6612(void)
{
    // 测试所有电机正转
    Motor_SetDuty(&Motor_Font_Left, 40.0f);
    Motor_SetDuty(&Motor_Font_Right, 40.0f);
    Motor_SetDuty(&Motor_Back_Left, 40.0f);
    Motor_SetDuty(&Motor_Back_Right, 40.0f);
    Delay(1000);
    Motor_SetDuty(&Motor_Font_Left, -40.0f);
    Motor_SetDuty(&Motor_Font_Right, -40.0f);
    Motor_SetDuty(&Motor_Back_Left, -40.0f);
    Motor_SetDuty(&Motor_Back_Right, -40.0f);
    Delay(1000);
    Motor_SetDuty(&Motor_Font_Left, 0.0f);
    Motor_SetDuty(&Motor_Font_Right, 0.0f);
    Motor_SetDuty(&Motor_Back_Left, 0.0f);
    Motor_SetDuty(&Motor_Back_Right, 0.0f);
   
}