******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 15:38:28 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000074a9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00009860  000167a0  R  X
  SRAM                  20200000   00008000  000006c1  0000793f  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00009860   00009860    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00008040   00008040    r-x .text
  00008100    00008100    000016d0   000016d0    r-- .rodata
  000097d0    000097d0    00000090   00000090    r-- .cinit
20200000    20200000    000004c2   00000000    rw-
  20200000    20200000    00000323   00000000    rw- .bss
  20200324    20200324    0000019e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00008040     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002b0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000139c    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001614    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000184c    0000022c     MPU6050.o (.text.Read_Quad)
                  00001a78    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001ca4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001ec4    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000020b8    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00002294    000001b0     Task.o (.text.Task_Start)
                  00002444    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  000025e4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00002776    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002778    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002900    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002a78    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002be8    00000168     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002d50    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002e94    0000013c     Tracker.o (.text.Tracker_Read)
                  00002fd0    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  0000310c    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003240    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003374    00000130     OLED.o (.text.OLED_ShowChar)
                  000034a4    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000035d4    00000128     inv_mpu.o (.text.mpu_init)
                  000036fc    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00003820    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003944    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003a64    00000118     main.o (.text.main)
                  00003b7c    00000110     OLED.o (.text.OLED_Init)
                  00003c8c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003d98    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003ea0    00000104     Task_App.o (.text.Task_Motor_PID)
                  00003fa4    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  000040a4    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004190    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004274    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004358    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00004434    000000dc     Task_App.o (.text.Task_OLED)
                  00004510    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000045e8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000046c0    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004794    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004864    000000cc     Motor.o (.text.Motor_Start)
                  00004930    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  000049f4    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004ab8    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004b7c    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004c38    000000b8     Motor.o (.text.Motor_SetDirc)
                  00004cf0    000000b8     Motor.o (.text.Motor_SetDuty)
                  00004da8    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004e60    000000b4     Task.o (.text.Task_Add)
                  00004f14    000000b0     Task_App.o (.text.Task_Init)
                  00004fc4    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00005070    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  0000511c    000000a4     Motor.o (.text.Motor_GetSpeed)
                  000051c0    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00005262    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00005264    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005304    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  000053a0    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  00005438    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000054d0    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00005566    00000002     --HOLE-- [fill = 0]
                  00005568    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000055f4    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005680    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005704    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005788    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000580a    00000002     --HOLE-- [fill = 0]
                  0000580c    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  0000588c    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  0000590c    00000080     Task_App.o (.text.Task_Serial)
                  0000598c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005a08    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005a7c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005a80    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005af4    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005b68    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005bd8    0000006e     OLED.o (.text.OLED_ShowString)
                  00005c46    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005cb0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005d18    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005d7e    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005de4    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005e48    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005eac    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005f10    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005f72    00000002     --HOLE-- [fill = 0]
                  00005f74    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005fd6    00000002     --HOLE-- [fill = 0]
                  00005fd8    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006038    00000060     Key_Led.o (.text.Key_Read)
                  00006098    00000060     Task_App.o (.text.Task_IdleFunction)
                  000060f8    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006158    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  000061b8    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00006216    00000002     --HOLE-- [fill = 0]
                  00006218    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00006274    0000005c     Task_App.o (.text.Task_Tracker)
                  000062d0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000632c    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00006384    00000058     Serial.o (.text.Serial_Init)
                  000063dc    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00006434    00000058            : _printfi.c.obj (.text._pconv_f)
                  0000648c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000064e2    00000002     --HOLE-- [fill = 0]
                  000064e4    00000054     Interrupt.o (.text.Interrupt_Init)
                  00006538    00000054     MPU6050.o (.text.mspm0_i2c_enable)
                  0000658c    00000054     OLED.o (.text.mspm0_i2c_enable)
                  000065e0    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00006632    00000002     --HOLE-- [fill = 0]
                  00006634    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006684    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000066d4    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006724    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006770    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000067bc    0000004c     OLED.o (.text.OLED_Printf)
                  00006808    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006852    00000002     --HOLE-- [fill = 0]
                  00006854    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000689c    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  000068e4    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  0000692c    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006970    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  000069b4    00000044     Task_App.o (.text.Task_Key)
                  000069f8    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006a3c    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006a80    00000044     MPU6050.o (.text.mspm0_i2c_disable)
                  00006ac4    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006b08    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00006b4a    00000002     --HOLE-- [fill = 0]
                  00006b4c    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006b8c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006bcc    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006c0c    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006c4c    0000003e     Task.o (.text.Task_CMP)
                  00006c8a    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006cc8    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006d04    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006d40    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006d7c    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006db8    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00006df4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006e30    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006e6c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006ea8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006ee2    00000002     --HOLE-- [fill = 0]
                  00006ee4    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006f1e    00000002     --HOLE-- [fill = 0]
                  00006f20    00000038     Task_App.o (.text.Task_LED)
                  00006f58    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006f90    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006fc4    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006ff8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000702c    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00007060    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00007092    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  000070c4    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  000070f4    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007124    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00007154    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007184    00000030            : vsnprintf.c.obj (.text._outs)
                  000071b4    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  000071e4    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007214    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00007240    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  0000726c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007298    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  000072c4    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  000072ee    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007316    00000028     OLED.o (.text.DL_Common_updateReg)
                  0000733e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007366    00000002     --HOLE-- [fill = 0]
                  00007368    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00007390    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000073b8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000073e0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007408    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007430    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007458    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007480    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000074a8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000074d0    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000074f6    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000751c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007542    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007568    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  0000758c    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  000075b0    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000075d2    00000002     --HOLE-- [fill = 0]
                  000075d4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000075f4    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007614    00000020     SysTick.o (.text.Delay)
                  00007634    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007654    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007672    00000002     --HOLE-- [fill = 0]
                  00007674    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007692    00000002     --HOLE-- [fill = 0]
                  00007694    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000076b0    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000076cc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000076e8    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007704    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007720    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000773c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007758    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007774    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007790    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  000077ac    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000077c8    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  000077e4    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007800    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000781c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007838    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007854    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007870    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  0000788c    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  000078a8    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000078c0    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  000078d8    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  000078f0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007908    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007920    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007938    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007950    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007968    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007980    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007998    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000079b0    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000079c8    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000079e0    00000018     OLED.o (.text.DL_GPIO_setPins)
                  000079f8    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00007a10    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007a28    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00007a40    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007a58    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007a70    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00007a88    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00007aa0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00007ab8    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00007ad0    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007ae8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00007b00    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007b18    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007b30    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007b48    00000018     OLED.o (.text.DL_I2C_reset)
                  00007b60    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007b78    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007b90    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007ba8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00007bc0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00007bd8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00007bf0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00007c08    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007c20    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007c38    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00007c50    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007c68    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007c80    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007c98    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007cb0    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00007cc8    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00007ce0    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00007cf8    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00007d10    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00007d28    00000018            : vsprintf.c.obj (.text._outs)
                  00007d40    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00007d56    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00007d6c    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00007d82    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007d98    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00007dae    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00007dc4    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007dda    00000016     SysTick.o (.text.SysGetTick)
                  00007df0    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00007e06    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007e1a    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007e2e    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007e42    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00007e56    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007e6a    00000002     --HOLE-- [fill = 0]
                  00007e6c    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007e80    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00007e94    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007ea8    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007ebc    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007ed0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007ee4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007ef8    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007f0c    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007f20    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007f34    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007f48    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007f5a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007f6c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007f7e    00000002     --HOLE-- [fill = 0]
                  00007f80    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007f90    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007fa0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007fb0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007fc0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007fce    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007fdc    0000000e     MPU6050.o (.text.tap_cb)
                  00007fea    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007ff8    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00008004    0000000c     SysTick.o (.text.Sys_GetTick)
                  00008010    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000801a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008024    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00008034    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000803e    00000002     --HOLE-- [fill = 0]
                  00008040    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00008050    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000805a    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008064    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000806e    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00008078    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00008088    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00008092    0000000a     MPU6050.o (.text.android_orient_cb)
                  0000809c    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  000080a4    00000008     Interrupt.o (.text.SysTick_Handler)
                  000080ac    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000080b4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000080bc    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  000080c2    00000002     --HOLE-- [fill = 0]
                  000080c4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  000080d4    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000080da    00000006            : exit.c.obj (.text:abort)
                  000080e0    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000080e4    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000080e8    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000080ec    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000080fc    00000004            : pre_init.c.obj (.text._system_pre_init)

.cinit     0    000097d0    00000090     
                  000097d0    0000006b     (.cinit..data.load) [load image, compression = lzss]
                  0000983b    00000001     --HOLE-- [fill = 0]
                  0000983c    0000000c     (__TI_handler_table)
                  00009848    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00009850    00000010     (__TI_cinit_table)

.rodata    0    00008100    000016d0     
                  00008100    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008cf6    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  000092e6    00000228     OLED_Font.o (.rodata.asc2_0806)
                  0000950e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009510    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009611    00000007     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00009618    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009658    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00009680    00000028     inv_mpu.o (.rodata.test)
                  000096a8    0000001e     inv_mpu.o (.rodata.reg)
                  000096c6    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000096c8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  000096e0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  000096f8    00000014     Task_App.o (.rodata.str1.11952760121962574671.1)
                  0000970c    00000014     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00009720    00000014     Task_App.o (.rodata.str1.492715258893803702.1)
                  00009734    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00009745    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00009756    00000011     Task_App.o (.rodata.str1.3850258909703972507.1)
                  00009767    00000011     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00009778    0000000c     inv_mpu.o (.rodata.hw)
                  00009784    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  0000978e    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00009790    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  00009798    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  000097a0    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  000097a8    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  000097ae    00000005     Task_App.o (.rodata.str1.11683036942922059812.1)
                  000097b3    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  000097b7    00000004     Task_App.o (.rodata.str1.16020955549137178199.1)
                  000097bb    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  000097be    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  000097c1    0000000f     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000323     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    00000010     (.common:quat)
                  20200300    00000006     (.common:Data_Accel)
                  20200306    00000006     (.common:Data_Gyro)
                  2020030c    00000004     (.common:Data_Pitch)
                  20200310    00000004     (.common:Data_Roll)
                  20200314    00000004     (.common:Data_Yaw)
                  20200318    00000004     (.common:ExISR_Flag)
                  2020031c    00000004     (.common:sensor_timestamp)
                  20200320    00000002     (.common:sensors)
                  20200322    00000001     (.common:more)

.data      0    20200324    0000019e     UNINITIALIZED
                  20200324    00000044     Motor.o (.data.Motor_Back_Left)
                  20200368    00000044     Motor.o (.data.Motor_Back_Right)
                  202003ac    00000044     Motor.o (.data.Motor_Font_Left)
                  202003f0    00000044     Motor.o (.data.Motor_Font_Right)
                  20200434    0000002c     inv_mpu.o (.data.st)
                  20200460    00000010     Task_App.o (.data.Motor)
                  20200470    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200480    0000000e     MPU6050.o (.data.hal)
                  2020048e    00000009     MPU6050.o (.data.gyro_orientation)
                  20200497    00000001     Task_App.o (.data.Flag_LED)
                  20200498    00000008     Task_App.o (.data.Data_MotorEncoder)
                  202004a0    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004a8    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004ac    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004b0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004b4    00000004     SysTick.o (.data.delayTick)
                  202004b8    00000004     SysTick.o (.data.uwTick)
                  202004bc    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004be    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004bf    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  202004c0    00000001     Task.o (.data.Task_Num)
                  202004c1    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3394    126       0      
       main.o                         280     0         0      
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3682    318       0      
                                                               
    .\APP\Src\
       Task_App.o                     1140    128       44     
       Interrupt.o                    622     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         1762    128       50     
                                                               
    .\BSP\Src\
       MPU6050.o                      2460    0         70     
       OLED_Font.o                    0       2072      0      
       OLED.o                         1846    0         0      
       Motor.o                        804     0         272    
       Serial.o                       404     0         512    
       Task.o                         674     0         241    
       PID_IQMath.o                   402     0         0      
       Tracker.o                      338     0         0      
       Key_Led.o                      118     0         0      
       SysTick.o                      106     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         7152    2072      1103   
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     292     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1048    0         0      
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/iqmath/lib/ticlang/m0p/mathacl/iqmath.a
       _IQNtoF.o                      48      0         0      
       _IQNdiv.o                      24      0         0      
       _IQNmpy.o                      24      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8356    355       4      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2984    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       143       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   32794   6160      1729   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00009850 records: 2, size/record: 8, table size: 16
	.data: load addr=000097d0, load size=0000006b bytes, run addr=20200324, run size=0000019e bytes, compression=lzss
	.bss: load addr=00009848, load size=00000008 bytes, run addr=20200000, run size=00000323 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000983c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000025e5     00008024     00008022   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004191     00008040     0000803c   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008058          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000806c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             000080a2          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             000080d8          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003c8d     00008078     00008076   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000025ef     000080c4     000080c0   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             000080e6          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000074a9     000080ec     000080e8   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00005a7d  ADC0_IRQHandler                      
00005a7d  ADC1_IRQHandler                      
00005a7d  AES_IRQHandler                       
000080e0  C$$EXIT                              
00005a7d  CANFD0_IRQHandler                    
00005a7d  DAC0_IRQHandler                      
00008011  DL_Common_delayCycles                
00006725  DL_DMA_initChannel                   
000061b9  DL_I2C_fillControllerTXFIFO          
00006d7d  DL_I2C_flushControllerTXFIFO         
00007543  DL_I2C_setClockConfig                
00004359  DL_SYSCTL_configSYSPLL               
00005de5  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000692d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00004931  DL_Timer_initPWMMode                 
00007839  DL_Timer_setCaptCompUpdateMethod     
00007c09  DL_Timer_setCaptureCompareOutCtl     
00007f91  DL_Timer_setCaptureCompareValue      
00007855  DL_Timer_setClockConfig              
00006855  DL_UART_init                         
00007f49  DL_UART_setClockConfig               
00005a7d  DMA_IRQHandler                       
20200300  Data_Accel                           
20200306  Data_Gyro                            
20200498  Data_MotorEncoder                    
202004a8  Data_Motor_TarSpeed                  
2020030c  Data_Pitch                           
20200310  Data_Roll                            
202004a0  Data_Tracker_Input                   
202004ac  Data_Tracker_Offset                  
20200314  Data_Yaw                             
00005a7d  Default_Handler                      
00007615  Delay                                
20200318  ExISR_Flag                           
20200497  Flag_LED                             
202004be  Flag_MPU6050_Ready                   
00005a7d  GROUP0_IRQHandler                    
00002be9  GROUP1_IRQHandler                    
000080e1  HOSTexit                             
00005a7d  HardFault_Handler                    
00005a7d  I2C0_IRQHandler                      
00005a7d  I2C1_IRQHandler                      
00005c47  I2C_OLED_Clear                       
00006db9  I2C_OLED_Set_Pos                     
000053a1  I2C_OLED_WR_Byte                     
00005fd9  I2C_OLED_i2c_sda_unlock              
000064e5  Interrupt_Init                       
00006039  Key_Read                             
00002d51  MPU6050_Init                         
20200460  Motor                                
20200324  Motor_Back_Left                      
20200368  Motor_Back_Right                     
202003ac  Motor_Font_Left                      
202003f0  Motor_Font_Right                     
0000511d  Motor_GetSpeed                       
00004c39  Motor_SetDirc                        
00004cf1  Motor_SetDuty                        
00004865  Motor_Start                          
00005b69  MyPrintf_DMA                         
00005a7d  NMI_Handler                          
00003b7d  OLED_Init                            
000067bd  OLED_Printf                          
00003375  OLED_ShowChar                        
00005bd9  OLED_ShowString                      
000072c5  PID_IQ_Init                          
000036fd  PID_IQ_Prosc                         
00006971  PID_IQ_SetParams                     
00005a7d  PendSV_Handler                       
00005a7d  RTC_IRQHandler                       
0000184d  Read_Quad                            
000080e9  Reset_Handler                        
00005a7d  SPI0_IRQHandler                      
00005a7d  SPI1_IRQHandler                      
00005a7d  SVC_Handler                          
000070f5  SYSCFG_DL_DMA_CH_RX_init             
00007cc9  SYSCFG_DL_DMA_CH_TX_init             
00007ff9  SYSCFG_DL_DMA_init                   
000010ed  SYSCFG_DL_GPIO_init                  
0000632d  SYSCFG_DL_I2C_MPU6050_init           
00005e49  SYSCFG_DL_I2C_OLED_init              
0000580d  SYSCFG_DL_MotorBack_init             
0000588d  SYSCFG_DL_MotorFront_init            
00006219  SYSCFG_DL_SYSCTL_init                
00007fa1  SYSCFG_DL_SYSTICK_init               
00005681  SYSCFG_DL_UART0_init                 
00007215  SYSCFG_DL_init                       
00005265  SYSCFG_DL_initPower                  
00006385  Serial_Init                          
20200000  Serial_RxData                        
00007ddb  SysGetTick                           
000080a5  SysTick_Handler                      
00007459  SysTick_Increasment                  
00008005  Sys_GetTick                          
00005a7d  TIMA0_IRQHandler                     
00005a7d  TIMA1_IRQHandler                     
00005a7d  TIMG0_IRQHandler                     
00005a7d  TIMG12_IRQHandler                    
00005a7d  TIMG6_IRQHandler                     
00005a7d  TIMG7_IRQHandler                     
00005a7d  TIMG8_IRQHandler                     
00007f5b  TI_memcpy_small                      
00007feb  TI_memset_small                      
00004e61  Task_Add                             
00006099  Task_IdleFunction                    
00004f15  Task_Init                            
000069b5  Task_Key                             
00006f21  Task_LED                             
00003ea1  Task_Motor_PID                       
00004435  Task_OLED                            
0000590d  Task_Serial                          
00002295  Task_Start                           
00006275  Task_Tracker                         
00002e95  Tracker_Read                         
00005a7d  UART0_IRQHandler                     
00005a7d  UART1_IRQHandler                     
00005a7d  UART2_IRQHandler                     
00005a7d  UART3_IRQHandler                     
00007ce1  _IQ24div                             
00007cf9  _IQ24mpy                             
00007125  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00009850  __TI_CINIT_Base                      
00009860  __TI_CINIT_Limit                     
00009860  __TI_CINIT_Warm                      
0000983c  __TI_Handler_Table_Base              
00009848  __TI_Handler_Table_Limit             
00006e6d  __TI_auto_init_nobinit_nopinit       
0000598d  __TI_decompress_lzss                 
00007f6d  __TI_decompress_none                 
000063dd  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00007df1  __TI_zero_init_nomemset              
000025ef  __adddf3                             
000045f3  __addsf3                             
00009510  __aeabi_ctype_table_                 
00009510  __aeabi_ctype_table_C                
00005a81  __aeabi_d2f                          
00006809  __aeabi_d2iz                         
00006b09  __aeabi_d2uiz                        
000025ef  __aeabi_dadd                         
00005f11  __aeabi_dcmpeq                       
00005f4d  __aeabi_dcmpge                       
00005f61  __aeabi_dcmpgt                       
00005f39  __aeabi_dcmple                       
00005f25  __aeabi_dcmplt                       
00003c8d  __aeabi_ddiv                         
00004191  __aeabi_dmul                         
000025e5  __aeabi_dsub                         
202004b0  __aeabi_errno                        
000080ad  __aeabi_errno_addr                   
00006b8d  __aeabi_f2d                          
00006f59  __aeabi_f2iz                         
000045f3  __aeabi_fadd                         
00005f75  __aeabi_fcmpeq                       
00005fb1  __aeabi_fcmpge                       
00005fc5  __aeabi_fcmpgt                       
00005f9d  __aeabi_fcmple                       
00005f89  __aeabi_fcmplt                       
00005789  __aeabi_fdiv                         
00005569  __aeabi_fmul                         
000045e9  __aeabi_fsub                         
0000726d  __aeabi_i2d                          
00006df5  __aeabi_i2f                          
0000648d  __aeabi_idiv                         
00002777  __aeabi_idiv0                        
0000648d  __aeabi_idivmod                      
00005263  __aeabi_ldiv0                        
00007675  __aeabi_llsl                         
0000758d  __aeabi_lmul                         
000080b5  __aeabi_memcpy                       
000080b5  __aeabi_memcpy4                      
000080b5  __aeabi_memcpy8                      
00007fc1  __aeabi_memset                       
00007fc1  __aeabi_memset4                      
00007fc1  __aeabi_memset8                      
00007481  __aeabi_ui2f                         
00006b4d  __aeabi_uidiv                        
00006b4d  __aeabi_uidivmod                     
00007ef9  __aeabi_uldivmod                     
00007675  __ashldi3                            
ffffffff  __binit__                            
00005cb1  __cmpdf2                             
00006ea9  __cmpsf2                             
00003c8d  __divdf3                             
00005789  __divsf3                             
00005cb1  __eqdf2                              
00006ea9  __eqsf2                              
00006b8d  __extendsfdf2                        
00006809  __fixdfsi                            
00006f59  __fixsfsi                            
00006b09  __fixunsdfsi                         
0000726d  __floatsidf                          
00006df5  __floatsisf                          
00007481  __floatunsisf                        
00005a09  __gedf2                              
00006e31  __gesf2                              
00005a09  __gtdf2                              
00006e31  __gtsf2                              
00005cb1  __ledf2                              
00006ea9  __lesf2                              
00005cb1  __ltdf2                              
00006ea9  __ltsf2                              
UNDEFED   __mpu_init                           
00004191  __muldf3                             
0000758d  __muldi3                             
00006ee5  __muldsi3                            
00005569  __mulsf3                             
00005cb1  __nedf2                              
00006ea9  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000025e5  __subdf3                             
000045e9  __subsf3                             
00005a81  __truncdfsf2                         
000051c1  __udivmoddi4                         
000074a9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000080fd  _system_pre_init                     
000080db  abort                                
000092e6  asc2_0806                            
00008cf6  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002779  atan2                                
00002779  atan2l                               
00000df5  atanl                                
00006bcd  atoi                                 
ffffffff  binit                                
202004b4  delayTick                            
0000689d  dmp_enable_6x_lp_quat                
0000139d  dmp_enable_feature                   
000060f9  dmp_enable_gyro_cal                  
000068e5  dmp_enable_lp_quat                   
0000788d  dmp_load_motion_driver_firmware      
00001ec5  dmp_read_fifo                        
00007f0d  dmp_register_android_orient_cb       
00007f21  dmp_register_tap_cb                  
00005439  dmp_set_fifo_rate                    
00002901  dmp_set_orientation                  
000069f9  dmp_set_shake_reject_thresh          
00007061  dmp_set_shake_reject_time            
00007093  dmp_set_shake_reject_timeout         
00005d7f  dmp_set_tap_axes                     
00006a3d  dmp_set_tap_count                    
00001615  dmp_set_tap_thresh                   
000071b5  dmp_set_tap_time                     
000071e5  dmp_set_tap_time_multi               
202004c1  enable_group1_irq                    
000062d1  frexp                                
000062d1  frexpl                               
00009778  hw                                   
00000000  interruptVectors                     
00004511  ldexp                                
00004511  ldexpl                               
00003a65  main                                 
000075b1  memccpy                              
00007635  memcmp                               
20200322  more                                 
00005ead  mpu6050_i2c_sda_unlock               
00004b7d  mpu_configure_fifo                   
00005af5  mpu_get_accel_fsr                    
00006159  mpu_get_gyro_fsr                     
0000702d  mpu_get_sample_rate                  
000035d5  mpu_init                             
00003821  mpu_load_firmware                    
00003fa5  mpu_lp_accel_mode                    
00003d99  mpu_read_fifo_stream                 
00004fc5  mpu_read_mem                         
00001a79  mpu_reset_fifo                       
00004275  mpu_set_accel_fsr                    
00002445  mpu_set_bypass                       
00004da9  mpu_set_dmp_state                    
000049f5  mpu_set_gyro_fsr                     
00005305  mpu_set_int_latched                  
00004795  mpu_set_lpf                          
000040a5  mpu_set_sample_rate                  
000034a5  mpu_set_sensors                      
00005071  mpu_write_mem                        
0000310d  mspm0_i2c_read                       
00004ab9  mspm0_i2c_write                      
00003241  qsort                                
202002f0  quat                                 
000096a8  reg                                  
00004511  scalbn                               
00004511  scalbnl                              
2020031c  sensor_timestamp                     
20200320  sensors                              
00002a79  sqrt                                 
00002a79  sqrtl                                
00009680  test                                 
202004b8  uwTick                               
00006c0d  vsnprintf                            
00007299  vsprintf                             
00007fb1  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  SYSCFG_DL_GPIO_init                  
0000139d  dmp_enable_feature                   
00001615  dmp_set_tap_thresh                   
0000184d  Read_Quad                            
00001a79  mpu_reset_fifo                       
00001ec5  dmp_read_fifo                        
00002295  Task_Start                           
00002445  mpu_set_bypass                       
000025e5  __aeabi_dsub                         
000025e5  __subdf3                             
000025ef  __adddf3                             
000025ef  __aeabi_dadd                         
00002777  __aeabi_idiv0                        
00002779  atan2                                
00002779  atan2l                               
00002901  dmp_set_orientation                  
00002a79  sqrt                                 
00002a79  sqrtl                                
00002be9  GROUP1_IRQHandler                    
00002d51  MPU6050_Init                         
00002e95  Tracker_Read                         
0000310d  mspm0_i2c_read                       
00003241  qsort                                
00003375  OLED_ShowChar                        
000034a5  mpu_set_sensors                      
000035d5  mpu_init                             
000036fd  PID_IQ_Prosc                         
00003821  mpu_load_firmware                    
00003a65  main                                 
00003b7d  OLED_Init                            
00003c8d  __aeabi_ddiv                         
00003c8d  __divdf3                             
00003d99  mpu_read_fifo_stream                 
00003ea1  Task_Motor_PID                       
00003fa5  mpu_lp_accel_mode                    
000040a5  mpu_set_sample_rate                  
00004191  __aeabi_dmul                         
00004191  __muldf3                             
00004275  mpu_set_accel_fsr                    
00004359  DL_SYSCTL_configSYSPLL               
00004435  Task_OLED                            
00004511  ldexp                                
00004511  ldexpl                               
00004511  scalbn                               
00004511  scalbnl                              
000045e9  __aeabi_fsub                         
000045e9  __subsf3                             
000045f3  __addsf3                             
000045f3  __aeabi_fadd                         
00004795  mpu_set_lpf                          
00004865  Motor_Start                          
00004931  DL_Timer_initPWMMode                 
000049f5  mpu_set_gyro_fsr                     
00004ab9  mspm0_i2c_write                      
00004b7d  mpu_configure_fifo                   
00004c39  Motor_SetDirc                        
00004cf1  Motor_SetDuty                        
00004da9  mpu_set_dmp_state                    
00004e61  Task_Add                             
00004f15  Task_Init                            
00004fc5  mpu_read_mem                         
00005071  mpu_write_mem                        
0000511d  Motor_GetSpeed                       
000051c1  __udivmoddi4                         
00005263  __aeabi_ldiv0                        
00005265  SYSCFG_DL_initPower                  
00005305  mpu_set_int_latched                  
000053a1  I2C_OLED_WR_Byte                     
00005439  dmp_set_fifo_rate                    
00005569  __aeabi_fmul                         
00005569  __mulsf3                             
00005681  SYSCFG_DL_UART0_init                 
00005789  __aeabi_fdiv                         
00005789  __divsf3                             
0000580d  SYSCFG_DL_MotorBack_init             
0000588d  SYSCFG_DL_MotorFront_init            
0000590d  Task_Serial                          
0000598d  __TI_decompress_lzss                 
00005a09  __gedf2                              
00005a09  __gtdf2                              
00005a7d  ADC0_IRQHandler                      
00005a7d  ADC1_IRQHandler                      
00005a7d  AES_IRQHandler                       
00005a7d  CANFD0_IRQHandler                    
00005a7d  DAC0_IRQHandler                      
00005a7d  DMA_IRQHandler                       
00005a7d  Default_Handler                      
00005a7d  GROUP0_IRQHandler                    
00005a7d  HardFault_Handler                    
00005a7d  I2C0_IRQHandler                      
00005a7d  I2C1_IRQHandler                      
00005a7d  NMI_Handler                          
00005a7d  PendSV_Handler                       
00005a7d  RTC_IRQHandler                       
00005a7d  SPI0_IRQHandler                      
00005a7d  SPI1_IRQHandler                      
00005a7d  SVC_Handler                          
00005a7d  TIMA0_IRQHandler                     
00005a7d  TIMA1_IRQHandler                     
00005a7d  TIMG0_IRQHandler                     
00005a7d  TIMG12_IRQHandler                    
00005a7d  TIMG6_IRQHandler                     
00005a7d  TIMG7_IRQHandler                     
00005a7d  TIMG8_IRQHandler                     
00005a7d  UART0_IRQHandler                     
00005a7d  UART1_IRQHandler                     
00005a7d  UART2_IRQHandler                     
00005a7d  UART3_IRQHandler                     
00005a81  __aeabi_d2f                          
00005a81  __truncdfsf2                         
00005af5  mpu_get_accel_fsr                    
00005b69  MyPrintf_DMA                         
00005bd9  OLED_ShowString                      
00005c47  I2C_OLED_Clear                       
00005cb1  __cmpdf2                             
00005cb1  __eqdf2                              
00005cb1  __ledf2                              
00005cb1  __ltdf2                              
00005cb1  __nedf2                              
00005d7f  dmp_set_tap_axes                     
00005de5  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005e49  SYSCFG_DL_I2C_OLED_init              
00005ead  mpu6050_i2c_sda_unlock               
00005f11  __aeabi_dcmpeq                       
00005f25  __aeabi_dcmplt                       
00005f39  __aeabi_dcmple                       
00005f4d  __aeabi_dcmpge                       
00005f61  __aeabi_dcmpgt                       
00005f75  __aeabi_fcmpeq                       
00005f89  __aeabi_fcmplt                       
00005f9d  __aeabi_fcmple                       
00005fb1  __aeabi_fcmpge                       
00005fc5  __aeabi_fcmpgt                       
00005fd9  I2C_OLED_i2c_sda_unlock              
00006039  Key_Read                             
00006099  Task_IdleFunction                    
000060f9  dmp_enable_gyro_cal                  
00006159  mpu_get_gyro_fsr                     
000061b9  DL_I2C_fillControllerTXFIFO          
00006219  SYSCFG_DL_SYSCTL_init                
00006275  Task_Tracker                         
000062d1  frexp                                
000062d1  frexpl                               
0000632d  SYSCFG_DL_I2C_MPU6050_init           
00006385  Serial_Init                          
000063dd  __TI_ltoa                            
0000648d  __aeabi_idiv                         
0000648d  __aeabi_idivmod                      
000064e5  Interrupt_Init                       
00006725  DL_DMA_initChannel                   
000067bd  OLED_Printf                          
00006809  __aeabi_d2iz                         
00006809  __fixdfsi                            
00006855  DL_UART_init                         
0000689d  dmp_enable_6x_lp_quat                
000068e5  dmp_enable_lp_quat                   
0000692d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006971  PID_IQ_SetParams                     
000069b5  Task_Key                             
000069f9  dmp_set_shake_reject_thresh          
00006a3d  dmp_set_tap_count                    
00006b09  __aeabi_d2uiz                        
00006b09  __fixunsdfsi                         
00006b4d  __aeabi_uidiv                        
00006b4d  __aeabi_uidivmod                     
00006b8d  __aeabi_f2d                          
00006b8d  __extendsfdf2                        
00006bcd  atoi                                 
00006c0d  vsnprintf                            
00006d7d  DL_I2C_flushControllerTXFIFO         
00006db9  I2C_OLED_Set_Pos                     
00006df5  __aeabi_i2f                          
00006df5  __floatsisf                          
00006e31  __gesf2                              
00006e31  __gtsf2                              
00006e6d  __TI_auto_init_nobinit_nopinit       
00006ea9  __cmpsf2                             
00006ea9  __eqsf2                              
00006ea9  __lesf2                              
00006ea9  __ltsf2                              
00006ea9  __nesf2                              
00006ee5  __muldsi3                            
00006f21  Task_LED                             
00006f59  __aeabi_f2iz                         
00006f59  __fixsfsi                            
0000702d  mpu_get_sample_rate                  
00007061  dmp_set_shake_reject_time            
00007093  dmp_set_shake_reject_timeout         
000070f5  SYSCFG_DL_DMA_CH_RX_init             
00007125  _IQ24toF                             
000071b5  dmp_set_tap_time                     
000071e5  dmp_set_tap_time_multi               
00007215  SYSCFG_DL_init                       
0000726d  __aeabi_i2d                          
0000726d  __floatsidf                          
00007299  vsprintf                             
000072c5  PID_IQ_Init                          
00007459  SysTick_Increasment                  
00007481  __aeabi_ui2f                         
00007481  __floatunsisf                        
000074a9  _c_int00_noargs                      
00007543  DL_I2C_setClockConfig                
0000758d  __aeabi_lmul                         
0000758d  __muldi3                             
000075b1  memccpy                              
00007615  Delay                                
00007635  memcmp                               
00007675  __aeabi_llsl                         
00007675  __ashldi3                            
00007839  DL_Timer_setCaptCompUpdateMethod     
00007855  DL_Timer_setClockConfig              
0000788d  dmp_load_motion_driver_firmware      
00007c09  DL_Timer_setCaptureCompareOutCtl     
00007cc9  SYSCFG_DL_DMA_CH_TX_init             
00007ce1  _IQ24div                             
00007cf9  _IQ24mpy                             
00007ddb  SysGetTick                           
00007df1  __TI_zero_init_nomemset              
00007ef9  __aeabi_uldivmod                     
00007f0d  dmp_register_android_orient_cb       
00007f21  dmp_register_tap_cb                  
00007f49  DL_UART_setClockConfig               
00007f5b  TI_memcpy_small                      
00007f6d  __TI_decompress_none                 
00007f91  DL_Timer_setCaptureCompareValue      
00007fa1  SYSCFG_DL_SYSTICK_init               
00007fb1  wcslen                               
00007fc1  __aeabi_memset                       
00007fc1  __aeabi_memset4                      
00007fc1  __aeabi_memset8                      
00007feb  TI_memset_small                      
00007ff9  SYSCFG_DL_DMA_init                   
00008005  Sys_GetTick                          
00008011  DL_Common_delayCycles                
000080a5  SysTick_Handler                      
000080ad  __aeabi_errno_addr                   
000080b5  __aeabi_memcpy                       
000080b5  __aeabi_memcpy4                      
000080b5  __aeabi_memcpy8                      
000080db  abort                                
000080e0  C$$EXIT                              
000080e1  HOSTexit                             
000080e9  Reset_Handler                        
000080fd  _system_pre_init                     
00008cf6  asc2_1608                            
000092e6  asc2_0806                            
00009510  __aeabi_ctype_table_                 
00009510  __aeabi_ctype_table_C                
00009680  test                                 
000096a8  reg                                  
00009778  hw                                   
0000983c  __TI_Handler_Table_Base              
00009848  __TI_Handler_Table_Limit             
00009850  __TI_CINIT_Base                      
00009860  __TI_CINIT_Limit                     
00009860  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  quat                                 
20200300  Data_Accel                           
20200306  Data_Gyro                            
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200314  Data_Yaw                             
20200318  ExISR_Flag                           
2020031c  sensor_timestamp                     
20200320  sensors                              
20200322  more                                 
20200324  Motor_Back_Left                      
20200368  Motor_Back_Right                     
202003ac  Motor_Font_Left                      
202003f0  Motor_Font_Right                     
20200460  Motor                                
20200497  Flag_LED                             
20200498  Data_MotorEncoder                    
202004a0  Data_Tracker_Input                   
202004a8  Data_Motor_TarSpeed                  
202004ac  Data_Tracker_Offset                  
202004b0  __aeabi_errno                        
202004b4  delayTick                            
202004b8  uwTick                               
202004be  Flag_MPU6050_Ready                   
202004c1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[312 symbols]
